from django.contrib.admin import AdminSite
from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.models import User, Group
from .models import Organization, Item, ManagedListValue, OrganizationUser

# Register OrganizationUser model
class OrganizationUserAdmin(admin.ModelAdmin):
    list_display = ('user', 'organization', 'is_admin', 'can_edit', 'can_add')
    list_filter = ('organization', 'is_admin', 'can_edit', 'can_add')
    search_fields = ('user__username', 'user__email', 'organization__name')
    
admin.site.register(OrganizationUser, OrganizationUserAdmin)

# Create the missing ItemOrgAdmin class
class ItemOrgAdmin(admin.ModelAdmin):
    list_display = ('item_name', 'get_item_code', 'rfid_epc', 'item_type', 'status', 'organization', 'date_added', 'last_updated', 'is_archived')
    list_filter = ('item_type', 'status', 'organization', 'is_archived')
    search_fields = ('item_name', 'item_description', 'rfid_epc')
    readonly_fields = ('date_added', 'last_updated', 'get_item_code')
    fieldsets = (
        (None, {
            'fields': ('item_name', 'item_description', 'get_item_code', 'rfid_epc')
        }),
        ('Classification', {
            'fields': ('item_type', 'status', 'organization')
        }),
        ('Location', {
            'fields': ('located_in',)
        }),
        ('Additional Data', {
            'fields': ('images', 'custom_fields')
        }),
        ('Metadata', {
            'fields': ('date_added', 'last_updated', 'is_archived')
        }),
    )
    
    def get_item_code(self, obj):
        return obj.item_code
    get_item_code.short_description = 'Item Code'
    
    def get_queryset(self, request):
        qs = super().get_queryset(request)
        # If we have an organization ID in the session, filter by it
        if hasattr(request, 'org_id') and request.org_id:
            return qs.filter(organization_id=request.org_id)
        return qs

class OrganizationAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'rfid_prefix', 'is_active')
    search_fields = ('name', 'code')
    list_filter = ('is_active',)
    
    fieldsets = (
        (None, {
            'fields': ('name', 'code', 'is_active')
        }),
        ('RFID Configuration', {
            'fields': ('rfid_prefix',),
            'description': 'The RFID prefix is used to generate unique EPCs for items in this organization.'
        }),
        ('Visual Identity', {
            'fields': ('logo', 'primary_color', 'secondary_color'),
            'classes': ('collapse',),
        }),
    )

admin.site.register(Organization, OrganizationAdmin)

class MultiOrgAdminSite(AdminSite):
    site_header = "Inventory Management"
    site_title = "Inventory Admin"
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.current_org_id = None
    
    def each_context(self, request):
        context = super().each_context(request)
        context['organizations'] = Organization.objects.filter(is_active=True)
        context['current_org_id'] = self.current_org_id
        return context

    def get_queryset(self, request, model):
        """Filter querysets by organization where applicable"""
        qs = model.objects.all()
        
        # If model has organization field and we have an org_id in session
        if hasattr(model, 'organization') and request.session.get('org_id'):
            org_id = request.session.get('org_id')
            self.current_org_id = org_id
            return qs.filter(organization_id=org_id)
        
        return qs

    def get_urls(self):
        from django.urls import path
        
        urls = super().get_urls()
        
        # Add custom URL patterns here if needed
        
        return urls

    def admin_view(self, view, cacheable=False):
        """Wrap the admin view to set organization context"""
        original_view = super().admin_view(view, cacheable)
        
        def inner(request, *args, **kwargs):
            # Set current org_id from session or query param
            if 'org_id' in request.GET:
                self.current_org_id = request.GET.get('org_id')
                request.session['org_id'] = self.current_org_id
            else:
                self.current_org_id = request.session.get('org_id')
            
            # Set org_id on request for use in admin views
            request.org_id = self.current_org_id
            
            return original_view(request, *args, **kwargs)
        
        return inner

# Create instance of custom admin site
org_admin = MultiOrgAdminSite(name='org_admin')

# Register models with the custom admin site
org_admin.register(Organization)
org_admin.register(Item, ItemOrgAdmin)  # Now using the defined ItemOrgAdmin class
org_admin.register(ManagedListValue)
org_admin.register(OrganizationUser, OrganizationUserAdmin)

# Register User and Group models with the custom admin site
org_admin.register(User, UserAdmin)
org_admin.register(Group)

