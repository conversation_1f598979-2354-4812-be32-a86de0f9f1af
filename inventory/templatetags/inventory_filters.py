from django import template
from django.utils.html import format_html
from inventory.models import Organization, Item

register = template.Library()

@register.filter
def filter_by_id(queryset, id_value):
    """Filter a queryset by ID and return the first item"""
    try:
        return queryset.get(id=id_value)
    except (Value<PERSON>rror, Organization.DoesNotExist):
        return None

@register.filter
def hex_to_rgb(hex_color):
    """Convert hex color to RGB format for CSS variables"""
    hex_color = hex_color.lstrip('#')
    try:
        return f"{int(hex_color[0:2], 16)}, {int(hex_color[2:4], 16)}, {int(hex_color[4:6], 16)}"
    except (ValueError, IndexError):
        return "0, 123, 255"  # Default bootstrap primary

@register.filter
def get_org_user(user, org_id):
    """Get OrganizationUser for a user and organization"""
    from inventory.models import OrganizationUser
    try:
        return OrganizationUser.objects.get(user=user, organization_id=org_id)
    except OrganizationUser.DoesNotExist:
        return None

@register.filter
def location_breadcrumb(item):
    """
    Generate a breadcrumb path for an item's location.
    Shows up to two levels of location hierarchy.
    Displays in reverse order: parent > grandparent
    """
    if not item or not item.located_in:
        return format_html('<span class="text-muted">Top Level</span>')

    # Get the parent location
    parent = item.located_in

    # Check if parent has a parent (grandparent)
    if parent.located_in:
        # Show parent > grandparent format (reversed order)
        grandparent = parent.located_in
        return format_html(
            '<a href="{}">{}</a> &gt; <a href="{}">{}</a>',
            f'/inventory/items/code/{parent.item_code}/',
            f'{parent.item_name}',
            f'/inventory/items/code/{grandparent.item_code}/',
            f'{grandparent.item_name}'
        )
    else:
        # Just show the parent
        return format_html(
            '<a href="{}">{}</a>',
            f'/inventory/items/code/{parent.item_code}/',
            f'{parent.item_name}'
        )

@register.filter
def pad_with_zeros(value, target_length):
    """Pad a string with leading zeros to reach target length"""
    value_str = str(value)
    return value_str.zfill(int(target_length))
