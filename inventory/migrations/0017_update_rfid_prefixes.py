# Generated by Django 5.0

from django.db import migrations


def update_org_prefixes(apps, schema_editor):
    """Update hierarchical prefixes for organizations"""
    Organization = apps.get_model('inventory', 'Organization')
    
    # Update all organizations with appropriate prefixes
    for org in Organization.objects.all():
        # Default prefix for existing organization with ID 2
        if org.pk == 2:  # Your first existing org has ID 2
            org.rfid_prefix = "00"
        else:
            # Generate a prefix based on ID, but skip "00" which is reserved for org ID 2
            org_id = org.pk
            if org.pk > 2:
                org_id = org.pk - 1  # Adjust to account for reserved "00"
            
            if org_id < 16:
                # First tier: single digit
                org.rfid_prefix = format(org_id, 'x')
            elif org_id < 256:
                # Second tier: two digits
                org.rfid_prefix = format(org_id, '02x')
            else:
                # Third tier: three digits
                org.rfid_prefix = format(org_id, '03x')
        org.save()


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0016_item_image_item_image_caption'),
    ]

    operations = [
        # Update organization prefixes
        migrations.RunPython(update_org_prefixes, migrations.RunPython.noop),
    ]