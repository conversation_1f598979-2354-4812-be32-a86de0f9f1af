from django.db import migrations, models
import django.core.validators

def assign_org_prefixes(apps, schema_editor):
    """Assign hierarchical prefixes to organizations"""
    Organization = apps.get_model('inventory', 'Organization')
    
    # Ensure all organizations have a prefix
    for org in Organization.objects.all():
        if not org.rfid_prefix:
            # Default prefix for existing organization with ID 2
            if org.pk == 2:  # Your first existing org has ID 2
                org.rfid_prefix = "00"
            else:
                # Generate a prefix based on ID, but skip "00" which is reserved for org ID 2
                org_id = org.pk
                if org.pk > 2:
                    org_id = org.pk - 1  # Adjust to account for reserved "00"
                
                if org_id < 16:
                    # First tier: single digit
                    org.rfid_prefix = format(org_id, 'x')
                elif org_id < 256:
                    # Second tier: two digits
                    org.rfid_prefix = format(org_id, '02x')
                else:
                    # Third tier: three digits
                    org.rfid_prefix = format(org_id, '03x')
            org.save()

def update_rfid_epcs(apps, schema_editor):
    """Update RFID EPCs for all items based on organization prefix and item code"""
    Item = apps.get_model('inventory', 'Item')
    
    # Calculate EPCs for all items
    for item in Item.objects.select_related('organization').all():
        if item.organization and item._item_code:
            # Get organization prefix and pad to 3 digits
            prefix = item.organization.rfid_prefix.ljust(3, '0')
            
            # Get item code as hex
            item_code = item._item_code.hex()
            
            # Calculate padding length (24 hex chars total)
            padding_length = 24 - len(prefix) - len(item_code)
            padding = "0" * padding_length
            
            # Combine: [prefix][padding][item_code]
            full_epc_hex = prefix + padding + item_code
            
            # Convert to binary and store
            item._rfid_epc = bytes.fromhex(full_epc_hex)
            
            # Use raw SQL to update to avoid validation issues
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute(
                    "UPDATE inventory_item SET rfid_epc = %s WHERE id = %s",
                    [item._rfid_epc, item.pk]
                )

class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0016_item_image_item_image_caption'),  # Update with your latest migration
    ]

    operations = [
        # 1. Add rfid_prefix to Organization
        migrations.AddField(
            model_name='organization',
            name='rfid_prefix',
            field=models.CharField(
                max_length=3,
                null=True,
                blank=True,
                unique=True,
                validators=[django.core.validators.RegexValidator(r'^[0-9a-f]{1,3}$', 'Prefix must be 1-3 hex characters')],
                help_text="Hierarchical hex prefix for RFID EPCs (1-3 characters)"
            ),
        ),
        
        # 2. Assign prefixes to organizations
        migrations.RunPython(assign_org_prefixes),
        
        # 3. Make rfid_prefix required after populating
        migrations.AlterField(
            model_name='organization',
            name='rfid_prefix',
            field=models.CharField(
                max_length=3,
                unique=True,
                validators=[django.core.validators.RegexValidator(r'^[0-9a-f]{1,3}$', 'Prefix must be 1-3 hex characters')],
                help_text="Hierarchical hex prefix for RFID EPCs (1-3 characters)"
            ),
        ),
        
        # 4. Update field size to exactly 12 bytes (96 bits)
        migrations.AlterField(
            model_name='item',
            name='_rfid_epc',
            field=models.BinaryField(
                blank=True,
                db_column='rfid_epc',
                editable=False,
                help_text='96-bit RFID Electronic Product Code',
                max_length=12,
                null=True,
                unique=True
            ),
        ),
        
        # 5. Update all EPCs based on organization prefix and item code
        migrations.RunPython(update_rfid_epcs),
    ]
