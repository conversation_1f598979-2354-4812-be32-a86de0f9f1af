# Generated by Django 5.0

from django.db import migrations


def inspect_and_update(apps, schema_editor):
    """Inspect the database schema and update RFID data"""
    Organization = apps.get_model('inventory', 'Organization')
    Item = apps.get_model('inventory', 'Item')
    
    # Print the field names for debugging
    print("Organization fields:", [f.name for f in Organization._meta.fields])
    print("Item fields:", [f.name for f in Item._meta.fields])
    
    # 1. Update organization prefixes
    for org in Organization.objects.all():
        # Default prefix for existing organization with ID 2
        if org.pk == 2:
            org.rfid_prefix = "00"
        else:
            # Generate a prefix based on ID, but skip "00" which is reserved for org ID 2
            org_id = org.pk
            if org.pk > 2:
                org_id = org.pk - 1  # Adjust to account for reserved "00"
            
            if org_id < 16:
                # First tier: single digit
                org.rfid_prefix = format(org_id, 'x')
            elif org_id < 256:
                # Second tier: two digits
                org.rfid_prefix = format(org_id, '02x')
            else:
                # Third tier: three digits
                org.rfid_prefix = format(org_id, '03x')
        org.save()
    
    # 2. Inspect the database schema to find the correct RFID EPC field
    from django.db import connection
    with connection.cursor() as cursor:
        # Get column names from the inventory_item table
        cursor.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'inventory_item'")
        columns = [row[0] for row in cursor.fetchall()]
        print("Database columns for inventory_item:", columns)
        
        # Find the RFID EPC column
        rfid_epc_column = None
        for col in columns:
            if 'rfid_epc' in col.lower():
                rfid_epc_column = col
                break
        
        if not rfid_epc_column:
            print("Could not find RFID EPC column in the database")
            return
        
        print(f"Found RFID EPC column: {rfid_epc_column}")
        
        # 3. Update item EPCs based on organization prefix and item code
        for item in Item.objects.select_related('organization').all():
            if item.organization and hasattr(item, '_item_code') and item._item_code:
                try:
                    # Get organization prefix and pad to 3 digits
                    prefix = item.organization.rfid_prefix.ljust(3, '0')
                    
                    # Get item code as hex
                    item_code = item._item_code.hex()
                    
                    # Calculate padding length (24 hex chars total)
                    padding_length = 24 - len(prefix) - len(item_code)
                    padding = "0" * padding_length
                    
                    # Combine: [prefix][padding][item_code]
                    full_epc_hex = prefix + padding + item_code
                    
                    # Convert to binary
                    binary_epc = bytes.fromhex(full_epc_hex)
                    
                    # Use the discovered column name
                    cursor.execute(
                        f"UPDATE inventory_item SET {rfid_epc_column} = %s WHERE id = %s",
                        [binary_epc, item.pk]
                    )
                except Exception as e:
                    print(f"Error updating EPC for item {item.pk}: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0022_merge_0018_update_rfid_epcs_0021_merge_20250617_2301'),
    ]

    operations = [
        # Inspect and update
        migrations.RunPython(inspect_and_update, migrations.RunPython.noop),
    ]