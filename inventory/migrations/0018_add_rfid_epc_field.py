# Generated by Django 5.0

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0017_add_rfid_prefix'),
    ]

    operations = [
        # Add the _rfid_epc field to Item model
        migrations.AddField(
            model_name='item',
            name='_rfid_epc',
            field=models.BinaryField(
                blank=True,
                db_column='rfid_epc',
                editable=False,
                help_text='96-bit RFID Electronic Product Code',
                max_length=12,
                null=True,
                unique=True
            ),
        ),
    ]