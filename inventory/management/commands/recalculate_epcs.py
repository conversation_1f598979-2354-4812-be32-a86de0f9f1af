from django.core.management.base import BaseCommand
from inventory.models import Item, Organization
from django.db import transaction

class Command(BaseCommand):
    help = 'Recalculate all 96-bit EPCs for items'

    def add_arguments(self, parser):
        parser.add_argument(
            '--org',
            type=int,
            help='Recalculate EPCs only for the specified organization ID'
        )

    def handle(self, *args, **options):
        count = 0
        # Process in batches to avoid memory issues
        batch_size = 1000
        
        # Filter by organization if specified
        items_query = Item.objects.all()
        if options['org']:
            try:
                org = Organization.objects.get(pk=options['org'])
                items_query = items_query.filter(organization=org)
                self.stdout.write(f"Recalculating EPCs for organization: {org.name}")
            except Organization.DoesNotExist:
                self.stdout.write(self.style.ERROR(f"Organization with ID {options['org']} not found"))
                return
        
        # Get total count for progress reporting
        total = items_query.count()
        self.stdout.write(f"Recalculating 96-bit EPCs for {total} items...")
        
        # Process in batches with transaction
        for i in range(0, total, batch_size):
            with transaction.atomic():
                items = items_query.select_related('organization')[i:i+batch_size]
                for item in items:
                    item._rfid_epc = item.calculate_epc_binary()
                    item.save(update_fields=['_rfid_epc'])
                    count += 1
                    
                    if count % 100 == 0:
                        self.stdout.write(f"Processed {count}/{total} items...")
        
        self.stdout.write(self.style.SUCCESS(f"Successfully recalculated 96-bit EPCs for {count} items"))