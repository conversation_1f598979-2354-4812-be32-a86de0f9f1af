# Generated by Django 5.0

from django.db import migrations


def update_org_prefixes_and_item_epcs(apps, schema_editor):
    """Update RFID prefixes and EPCs for all organizations and items"""
    Organization = apps.get_model('inventory', 'Organization')
    Item = apps.get_model('inventory', 'Item')
    
    # 1. Update organization prefixes
    for org in Organization.objects.all():
        # Default prefix for existing organization with ID 2
        if org.pk == 2:
            org.rfid_prefix = "00"
        else:
            # Generate a prefix based on ID, but skip "00" which is reserved for org ID 2
            org_id = org.pk
            if org.pk > 2:
                org_id = org.pk - 1  # Adjust to account for reserved "00"
            
            if org_id < 16:
                # First tier: single digit
                org.rfid_prefix = format(org_id, 'x')
            elif org_id < 256:
                # Second tier: two digits
                org.rfid_prefix = format(org_id, '02x')
            else:
                # Third tier: three digits
                org.rfid_prefix = format(org_id, '03x')
        org.save()
    
    # 2. Update item EPCs based on organization prefixes
    for item in Item.objects.select_related('organization').all():
        if item.organization and hasattr(item, '_item_code') and item._item_code:
            try:
                # Get organization prefix and pad to 3 digits
                prefix = item.organization.rfid_prefix.ljust(3, '0')
                
                # Get item code as hex
                item_code = item._item_code.hex()
                
                # Calculate padding length (24 hex chars total)
                padding_length = 24 - len(prefix) - len(item_code)
                padding = "0" * padding_length
                
                # Combine: [prefix][padding][item_code]
                full_epc_hex = prefix + padding + item_code
                
                # Convert to binary
                binary_epc = bytes.fromhex(full_epc_hex)
                
                # Use raw SQL to update to avoid validation issues
                # Try different field names that might be used in the database
                from django.db import connection
                with connection.cursor() as cursor:
                    try:
                        # First try with _rfid_epc (which might be the actual field name)
                        cursor.execute(
                            "UPDATE inventory_item SET _rfid_epc = %s WHERE id = %s",
                            [binary_epc, item.pk]
                        )
                    except Exception:
                        try:
                            # Then try with rfid_epc
                            cursor.execute(
                                "UPDATE inventory_item SET rfid_epc = %s WHERE id = %s",
                                [binary_epc, item.pk]
                            )
                        except Exception as e:
                            print(f"Error updating EPC for item {item.pk}: {e}")
            except Exception as e:
                print(f"Error processing item {item.pk}: {e}")


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0022_merge_0018_update_rfid_epcs_0021_merge_20250617_2301'),
    ]

    operations = [
        # Update organization prefixes and item EPCs
        migrations.RunPython(update_org_prefixes_and_item_epcs, migrations.RunPython.noop),
    ]
